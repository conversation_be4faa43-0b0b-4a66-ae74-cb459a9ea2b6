import { Button } from '@repo/ui/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/ui/dialog';
import { Input } from '@repo/ui/components/ui/input';
import { Textarea } from '@repo/ui/components/ui/textarea';
import type { Editor } from '@tiptap/react';
import { useEffect, useState } from 'react';
import { getWorkflow } from '../../editor-kit/extensions/thought-adpater-extension';
import { ThoughtHistoryModal } from './history-modal';

interface AddHistoryModalProps {
  editor?: Editor;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AddHistoryModal = (props: AddHistoryModalProps) => {
  const { editor, open, onOpenChange } = props;
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [historyModalOpen, setHistoryModalOpen] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (!open) {
      setTitle('');
      setDescription('');
    }
  }, [open]);

  const onSaveVersion = async () => {
    if (!editor) {
      return;
    }
    const workflow = getWorkflow(editor);
    if (!workflow) {
      return;
    }
    setSaveLoading(true);
    const newVersion = await workflow.createManualVersion(title, description);
    setSaveLoading(false);
    if (newVersion) {
      // editor.commands.showSuccessToast("Version saved successfully");
      onOpenChange(false);
    } else {
      // editor.commands.showFailToast("Failed to save version");
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          className="h-[18.5rem] w-[25rem] bg-white px-5 py-5"
          // todo
          // closeIconClassName="w-5 h-5 " overlayClassName="bg-black/45"
        >
          <DialogHeader>
            <DialogTitle>
              <div className="text-left text-base font-[590]">Save to history version</div>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col">
            <Input
              maxLength={60}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Title"
              className="h-[2.5rem] border border-muted bg-white placeholder:text-disabled-foreground"
            />
            <Textarea
              maxLength={255}
              placeholder="Description (optional)"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={1}
              className="mt-3 h-[6.25rem] resize-none border border-muted bg-white placeholder:text-disabled-foreground"
            />
            <div className="flex items-center justify-between mt-6">
              <div
                className="text-sm cursor-pointer"
                onClick={() => {
                  onOpenChange(false);
                  setHistoryModalOpen(true);
                }}
              >
                Show all versions
              </div>
              <div className="flex items-center gap-x-3">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button disabled={!title} loading={saveLoading} onClick={onSaveVersion}>
                  Save
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ThoughtHistoryModal
        editor={editor}
        open={historyModalOpen}
        onOpenChange={setHistoryModalOpen}
      />
    </>
  );
};
