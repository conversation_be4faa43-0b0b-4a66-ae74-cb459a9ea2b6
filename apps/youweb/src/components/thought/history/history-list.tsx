import { Button } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Input } from '@repo/ui/components/ui/input';
import { toast } from '@repo/ui/components/ui/sonner';
import { Textarea } from '@repo/ui/components/ui/textarea';
import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/react';
import { format } from 'date-fns';
import { Ellipsis } from 'lucide-react';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { EmptySearchResult } from '@/components/board/board-masonry/empty-search-result';
import { getWorkflow } from '@/components/editor-kit/extensions/thought-adpater-extension';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { HistoryAiItemIcon } from '../icon/history-ai-item';
import type { ThoughtVersion } from './tem-type';
import { ThoughtVersionTypeEnum } from './tem-type';

export interface HistoryItemProps {
  curThought?: ThoughtVersion;
  thoughtVersion: ThoughtVersion;
  editor?: Editor;
  onSetCurThought: (thought: ThoughtVersion) => void;
  onDeleteVersion: (versionToDelete: ThoughtVersion) => void;
}

interface HistoryListProps {
  curThought?: ThoughtVersion;
  thoughtVersionList: ThoughtVersion[];
  editor?: Editor;
  onSetCurThought: (thought: ThoughtVersion) => void;
  onDeleteVersion: (versionToDelete: ThoughtVersion) => void;
  onAddVersion: (newVersion: ThoughtVersion) => void;
}

export interface HistoryListRef {
  openEditHistoryItem: () => void;
}

const AIIcon = () => {
  return (
    <div className="flex h-3 w-3 flex-shrink-0 items-center justify-center rounded-full border border-white bg-[#A25AD9]">
      <HistoryAiItemIcon size={8} fill="white" />
    </div>
  );
};

const getFullUrlImageUrl = (url?: string) => {
  if (!url) {
    return '';
  }
  if (url.startsWith('files/')) {
    return `/${url}`;
  } else {
    return url;
  }
};

export const HistoryItem = (props: HistoryItemProps) => {
  const { curThought, thoughtVersion, editor, onSetCurThought, onDeleteVersion } = props;
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const user = useMemo(() => {
    if (!editor) {
      return null;
    }
    const workflow = getWorkflow(editor);
    if (!workflow) {
      return null;
    }
    return workflow.userInfo;
  }, [editor]);

  const handleDelete = (event: React.MouseEvent) => {
    event.stopPropagation();
    event.preventDefault();
    onDeleteVersion(thoughtVersion);
    setIsMenuOpen(false);
  };

  return (
    <div
      key={thoughtVersion.id}
      className={cn(
        'group flex cursor-pointer flex-col gap-y-1 rounded-md p-2 transition-transform hover:bg-card-snips',
        curThought?.id === thoughtVersion.id && 'bg-card-snips',
        isMenuOpen && 'bg-card-snips',
      )}
      onClick={() => onSetCurThought(thoughtVersion)}
    >
      <div className="flex items-center gap-x-1">
        <div className="relative flex-shrink-0 w-5 h-5 border rounded-full border-muted">
          <Avatar className="w-full h-full">
            <AvatarImage
              src={getFullUrlImageUrl(user?.picture)}
              alt="user avatar"
              className="object-cover"
            />
            <AvatarFallback></AvatarFallback>
          </Avatar>
          {thoughtVersion.type === ThoughtVersionTypeEnum.AI && (
            <div className="absolute -top-1 -right-1">
              <AIIcon />
            </div>
          )}
        </div>
        <div className="flex-1 overflow-hidden text-sm font-medium whitespace-nowrap text-ellipsis text-foreground">
          {thoughtVersion.title}
        </div>
        <div>
          <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <DropdownMenuTrigger>
              <div
                className={cn(
                  'invisible rounded-[4px] p-1 transition-transform hover:bg-muted group-hover:visible',
                  isMenuOpen && 'visible bg-muted',
                )}
              >
                <Ellipsis size={16} className="text-caption" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="p-1 border-none rounded-lg shadow-lg">
              <DropdownMenuItem className="text-sm cursor-pointer" onClick={handleDelete}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {thoughtVersion.description && (
        <div className={'text-xs whitespace-normal break-words text-muted-foreground'}>
          {thoughtVersion.description}
        </div>
      )}
      <div className={'text-2xs text-caption'}>
        {format(thoughtVersion.created_at, 'MMMM d, yyyy, h:mm a')}
      </div>
    </div>
  );
};

const EditHistoryItem = ({
  onSave,
  onCancel,
  afterSuccess,
}: {
  onSave: (
    title: string,
    description: string,
  ) => Promise<{
    success: boolean;
    newVersion: ThoughtVersion | null;
  }>;
  onCancel: () => void;
  afterSuccess: (newVersion: ThoughtVersion) => void;
}) => {
  const [title, setTitle] = useState(
    `Saved by You at ${format(Date.now(), 'MMMM d, yyyy, h:mm a')}`,
  );
  const [description, setDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    const { success, newVersion } = await onSave(title, description);
    setIsSaving(false);
    if (success && newVersion) {
      afterSuccess(newVersion);
    }
  };

  return (
    <div className="flex h-[7.125rem] w-full flex-col rounded-lg border border-muted p-2">
      <Input
        maxLength={60}
        placeholder="Title"
        className="h-[1.25rem] border-none bg-white p-0 text-sm"
        autoFocus
        value={title}
        onChange={(e) => setTitle(e.target.value)}
      />
      <Textarea
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        maxLength={255}
        placeholder="Description (optional)"
        rows={1}
        className="mt-1 h-[3rem] min-h-[2.75rem] resize-none border-none bg-white p-0 placeholder:text-disabled-foreground"
      />
      <div className="flex justify-end mt-1 gap-x-2">
        <Button variant="outline" size="sm" onClick={onCancel}>
          Cancel
        </Button>
        <Button loading={isSaving} disabled={!title} size="sm" onClick={handleSave}>
          Save
        </Button>
      </div>
    </div>
  );
};

export const HistoryList = forwardRef<HistoryListRef, HistoryListProps>((props, ref) => {
  const { curThought, thoughtVersionList, editor, onSetCurThought, onDeleteVersion, onAddVersion } =
    props;
  const [showEditHistory, setShowEditHistory] = useState(false);

  const handleSave = async (
    title: string,
    description: string,
  ): Promise<{
    success: boolean;
    newVersion: ThoughtVersion | null;
  }> => {
    if (!editor) {
      return {
        success: false,
        newVersion: null,
      };
    }
    const workflow = getWorkflow(editor);
    if (!workflow) {
      return {
        success: false,
        newVersion: null,
      };
    }
    const newVersion = await workflow.createManualVersion(title, description);
    if (newVersion) {
      toast('Version saved successfully');
      return {
        success: true,
        newVersion,
      };
    } else {
      toast('Failed to save version');
      return {
        success: false,
        newVersion: null,
      };
    }
  };

  useImperativeHandle(ref, () => ({
    openEditHistoryItem: () => {
      setShowEditHistory(true);
    },
  }));

  const renderItem = (item: ThoughtVersion) => {
    return (
      <HistoryItem
        curThought={curThought}
        key={item.id}
        thoughtVersion={item}
        editor={editor}
        onSetCurThought={onSetCurThought}
        onDeleteVersion={onDeleteVersion}
      />
    );
  };

  if (thoughtVersionList.length === 0 && !showEditHistory) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <EmptySearchResult />
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-full mt-3 overflow-y-auto gap-y-1">
      {showEditHistory && (
        <EditHistoryItem
          onSave={handleSave}
          afterSuccess={(newVersion) => {
            setShowEditHistory(false);
            onAddVersion(newVersion);
          }}
          onCancel={() => {
            setShowEditHistory(false);
          }}
        />
      )}
      {thoughtVersionList.map(renderItem)}
    </div>
  );
});

HistoryList.displayName = 'HistoryList';
