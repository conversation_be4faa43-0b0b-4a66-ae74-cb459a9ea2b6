import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Check } from 'lucide-react';
import type { ReactNode } from 'react';
import { ThoughtVersionTypeEnum } from './tem-type';

interface FilterVersionTypeMenuProps {
  children: ReactNode;
  onFilterVersionTypeChange: (value: ThoughtVersionTypeEnum | undefined) => void;
  filterVersionType: ThoughtVersionTypeEnum | undefined;
  onOpenChange?: (open: boolean) => void;
}

const FilterVersionTypeMenuConfig = [
  {
    label: 'All versions',
    value: undefined,
  },
  {
    label: 'Saved manually',
    value: ThoughtVersionTypeEnum.MANUAL,
  },
  {
    label: 'Saved by AI',
    value: ThoughtVersionTypeEnum.AI,
  },
  {
    label: 'Auto saved',
    value: ThoughtVersionTypeEnum.AUTO,
  },
];

export const FilterVersionTypeMenu = (props: FilterVersionTypeMenuProps) => {
  const { children, onFilterVersionTypeChange, filterVersionType, onOpenChange } = props;

  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="start"
        className="w-[13.25rem] rounded-lg border-none text-sm shadow-lg"
      >
        {FilterVersionTypeMenuConfig.map((item) => (
          <DropdownMenuItem
            key={item.value}
            className="cursor-pointer"
            onClick={() => {
              onFilterVersionTypeChange(item.value);
            }}
          >
            <div className="flex items-center justify-between w-full">
              <div>{item.label}</div>
              {item.value === filterVersionType && (
                <div className="text-primary">
                  <Check size={16} />
                </div>
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
