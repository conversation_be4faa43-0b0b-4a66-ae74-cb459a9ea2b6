import { Button } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { Editor } from '@tiptap/react';
import { ListFilter, Plus, X } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import { getWorkflow } from '../../editor-kit/extensions/thought-adpater-extension';
import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { FilterVersionTypeMenu } from './filter-version-type-menu';
import { HistoryList, type HistoryListRef } from './history-list';
import type { ThoughtVersion, ThoughtVersionTypeEnum } from './tem-type';

interface HistoryInfoProps {
  editor?: Editor;
  thoughtVersionList: ThoughtVersion[];
  onSetCurThought: (thought: ThoughtVersion) => void;
  curThought?: ThoughtVersion;
  onDeleteVersion: (versionToDelete: ThoughtVersion) => void;
  onAddVersion: (newVersion: ThoughtVersion) => void;
  onRestoreSuccess: () => void;
  onOpenChange: (open: boolean) => void;
}

export const HistoryInfo = (props: HistoryInfoProps) => {
  const {
    editor,
    thoughtVersionList,
    onSetCurThought,
    curThought,
    onDeleteVersion,
    onAddVersion,
    onRestoreSuccess,
    onOpenChange,
  } = props;
  const [loading, setLoading] = useState(false);
  const [filterVersionType, setFilterVersionType] = useState<ThoughtVersionTypeEnum | undefined>(
    undefined,
  );
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const historyListRef = useRef<HistoryListRef>(null);

  const handleRestore = useCallback(async () => {
    if (!editor || !curThought) {
      return;
    }
    const workflow = getWorkflow(editor);
    if (!workflow) {
      return;
    }
    setLoading(true);
    const success = await workflow.restoreThought(curThought);
    setLoading(false);
    if (!success) {
      toast('Restore failed');
    } else {
      onRestoreSuccess();
    }
  }, [editor, curThought, onRestoreSuccess]);

  const renderHistoryOperationButton = () => {
    return (
      <div className="flex justify-end w-full pr-5 mt-1">
        <Button
          className="h-8 py-0 rounded-full px-9"
          loading={loading}
          onClick={handleRestore}
          disabled={!curThought}
        >
          Restore
        </Button>
      </div>
    );
  };
  return (
    <div className="flex flex-col w-full h-full gap-y-1">
      <div className="flex items-center justify-between h-8 px-2">
        <div className="text-left text-base font-[590]">History versions</div>
        <div className="flex items-center gap-x-2">
          <TooltipProvider>
            <Tooltip open={isFilterMenuOpen ? false : undefined}>
              <TooltipTrigger>
                <FilterVersionTypeMenu
                  onFilterVersionTypeChange={setFilterVersionType}
                  filterVersionType={filterVersionType}
                  onOpenChange={setIsFilterMenuOpen}
                >
                  <div className="flex items-center justify-center w-6 h-6 rounded-md cursor-pointer hover:bg-card-snips">
                    <ListFilter size={18} />
                  </div>
                </FilterVersionTypeMenu>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>Filter</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div
                  className="flex items-center justify-center w-6 h-6 rounded-md cursor-pointer hover:bg-card-snips"
                  onClick={() => {
                    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_HISTORY_MODAL_ADD_VERSION);
                    historyListRef.current?.openEditHistoryItem();
                  }}
                >
                  <Plus size={18} />
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>Add version</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div
            className="flex items-center justify-center w-6 h-6 rounded-md cursor-pointer hover:bg-card-snips"
            onClick={() => {
              onOpenChange(false);
            }}
          >
            <X size={18} />
          </div>
        </div>
      </div>
      <HistoryList
        ref={historyListRef}
        curThought={curThought}
        thoughtVersionList={thoughtVersionList.filter((item) => {
          if (filterVersionType === undefined) {
            return true;
          }
          return item.type === filterVersionType;
        })}
        editor={editor}
        onSetCurThought={onSetCurThought}
        onDeleteVersion={onDeleteVersion}
        onAddVersion={onAddVersion}
      />
      {renderHistoryOperationButton()}
    </div>
  );
};
