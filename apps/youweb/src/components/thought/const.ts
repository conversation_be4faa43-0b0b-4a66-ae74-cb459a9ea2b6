export enum EDITOR_EKY_MAP {
  Enter = 'Enter',
}

export enum SelectionType {
  CURSOR = 'cursor',
  SELECTION = 'selection',
}

// 导航键和功能键列表，这些键不应该触发数据更新
export const NAVIGATION_KEYS = [
  'ArrowUp',
  'ArrowDown',
  'ArrowLeft',
  'ArrowRight',
  'Home',
  'End',
  'PageUp',
  'PageDown',
  'Tab',
  'Escape',
  'CapsLock',
  'Shift',
  'Control',
  'Alt',
  'Meta',
  'ContextMenu',
] as const;

// 检查是否为导航键或功能键
export const isNavigationOrFunctionKey = (key: string): boolean => {
  return NAVIGATION_KEYS.includes(key as (typeof NAVIGATION_KEYS)[number]) || key.startsWith('F');
};

export const THOUGHT_EVENT_REPORT_KEY = {
  THOUGHT_TOOLBAR_TRANSLATE_CONTENT_MENU_CLICK: 'thought_toolbar_translate_content_menu_click',
  THOUGHT_HISTORY_MODAL_OPEN: 'thought_history_modal_open',
  THOUGHT_HISTORY_MODAL_ADD_VERSION: 'thought_history_modal_add_version',
  // Text Menu 相关埋点
  THOUGHT_TEXT_MENU_ASK_AI_CLICK: 'thought_text_menu_ask_ai_click',
  THOUGHT_TEXT_MENU_BOLD_CLICK: 'thought_text_menu_bold_click',
  THOUGHT_TEXT_MENU_LINK_CLICK: 'thought_text_menu_link_click',
  // Format Content Menu 相关埋点
  THOUGHT_FORMAT_CONTENT_MENU_ITEM_CLICK: 'thought_format_content_menu_item_click',
};
