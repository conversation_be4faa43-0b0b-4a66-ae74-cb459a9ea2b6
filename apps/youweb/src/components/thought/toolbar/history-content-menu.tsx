import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import type { Editor } from '@tiptap/react';
import { type ReactNode, useState } from 'react';
import { AddHistoryModal } from '../history/add-history-modal';
import { ThoughtHistoryModal } from '../history/history-modal';
import { ThoughtAddHistoryVersion } from '../icon/thought-add-history-version';
import { ThoughtHistoryIcon } from '../icon/thought-history';

interface HistoryContentMenuProps {
  editor: Editor;
  children?: ReactNode | undefined;
  onOpenChange?: (open: boolean) => void;
}

export const HistoryContentMenu = (props: HistoryContentMenuProps) => {
  const { editor, children, onOpenChange } = props;
  const [addHistoryModalOpen, setAddHistoryModalOpen] = useState(false);
  const [historyPreviewModalOpen, setHistoryPreviewModalOpen] = useState(false);

  const handleAddToHistory = () => {
    setAddHistoryModalOpen(true);
  };

  const handleHistoryPreview = () => {
    setHistoryPreviewModalOpen(true);
  };

  return (
    <>
      <DropdownMenu onOpenChange={onOpenChange}>
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-[12.5rem] rounded-lg border-none shadow-lg"
          side="left"
          align="start"
        >
          <div className="flex flex-col">
            <DropdownMenuItem className="cursor-pointer" onClick={handleAddToHistory}>
              <div className="flex items-center gap-2">
                <ThoughtAddHistoryVersion size={16} />
                Add a version
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer" onClick={handleHistoryPreview}>
              <div className="flex items-center gap-2">
                <ThoughtHistoryIcon size={16} />
                Version history
              </div>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <AddHistoryModal
        editor={editor}
        open={addHistoryModalOpen}
        onOpenChange={setAddHistoryModalOpen}
      />

      <ThoughtHistoryModal
        editor={editor}
        open={historyPreviewModalOpen}
        onOpenChange={setHistoryPreviewModalOpen}
      />
    </>
  );
};
