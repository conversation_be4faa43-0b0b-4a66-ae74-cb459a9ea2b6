import { Button } from '@repo/ui/components/ui/button';
import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/react';
import { ArrowUp } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ThoughtAskAIIcon } from '../icon/thought-ask-ai-icon';
import { invokeAICommand } from '../utils';

interface AskAIModalProps {
  editor: Editor;
  closeModal: () => void;
}

export const AskAIModal = (props: AskAIModalProps) => {
  const { editor, closeModal } = props;
  const [askContent, setAskContent] = useState('');
  const divRef = useRef<HTMLDivElement>(null);

  const onClickAskAI = useCallback(() => {
    const res = invokeAICommand({
      editor,
      commandOrMessage: askContent,
    });
    if (res) {
      closeModal();
    }
  }, [askContent, editor, closeModal]);

  useEffect(() => {
    if (divRef.current) {
      // 在页面挂载的时候，就要聚焦在这里
      setTimeout(() => {
        divRef.current?.focus();
      }, 100);
    }
  }, [editor]);

  const handleInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const content = element.textContent || '';

    // 如果内容为空但还有HTML内容（如<br>），清理它
    if (!content.trim() && element.innerHTML !== '') {
      element.innerHTML = '';
    }

    setAskContent(content);
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {
        e.preventDefault();
        if (askContent.trim()) {
          onClickAskAI();
        }
      }
    },
    [askContent, onClickAskAI],
  );

  useEffect(() => {
    if (divRef.current && askContent !== divRef.current.textContent) {
      divRef.current.textContent = askContent;
    }
  }, [askContent]);

  return (
    <div className="border-full w-[31.75rem] overflow-hidden rounded-xl border border-divider bg-card shadow-md">
      <div className="flex items-center p-4 gap-x-2 border-divider">
        <ThoughtAskAIIcon size={16} className="mt-1 shrink-0 self-start text-[#A25AD9]" />
        <div
          ref={divRef}
          contentEditable
          suppressContentEditableWarning
          className={cn(
            'overflow-y-scroll p-0 w-full h-auto text-sm bg-transparent border-none outline-none max-h-[72px] min-h-5',
            'placeholder:text-disabled-foreground',
            'empty:before:text-disabled-foreground empty:before:content-[attr(data-placeholder)]',
          )}
          data-placeholder="Ask anything"
          onInput={handleInput}
          onKeyDown={handleKeyDown}
        />
        <Button
          className="self-end w-6 h-6 p-0 rounded-full shrink-0"
          disabled={!askContent}
          onClick={onClickAskAI}
        >
          <ArrowUp size={12} />
        </Button>
      </div>
      {/* <div className="flex flex-col">
        {AskAICommandList.map((command) => (
          <div
            className="flex items-center px-4 py-2 cursor-pointer hover:bg-card-snips"
            key={command.label}
            onClick={() => {
              if (command.onClick(editor)) {
                closeModal();
              }
            }}
          >
            {command.icon}
            <div className="ml-2 text-sm">{command.label}</div>
          </div>
        ))}
      </div> */}
    </div>
  );
};
